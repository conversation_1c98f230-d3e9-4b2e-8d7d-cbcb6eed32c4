# 燕友圈榜单系统 - 完整API接口文档

## 📋 目录

- [1. 内容管理模块](#1-内容管理模块)
- [2. 播报列表模块](#2-播报列表模块)
- [3. 用户列表管理模块](#3-用户列表管理模块)
- [4. 用户权限管理模块](#4-用户权限管理模块)
- [5. 配置管理模块](#5-配置管理模块)
- [6. 赞助商管理模块](#6-赞助商管理模块)

## 🔧 通用说明

### 认证方式
所有需要认证的接口都使用 Bearer Token 认证：
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {...},
  "timestamp": "2024-01-01T00:00:00"
}
```

### 分页响应格式
```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "size": 10,
  "pages": 10
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "detail": "具体错误信息",
  "timestamp": "2024-01-01T00:00:00"
}
```

## 1. 内容管理模块

### 1.1 获取内容列表

**接口**: `GET /api/v1/content/contents`  
**描述**: 获取内容列表，支持分页和筛选  
**认证**: Bearer Token (管理员权限)

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| content_type | string | 否 | - | 内容类型筛选 |
| is_published | boolean | 否 | - | 发布状态筛选 |
| search | string | 否 | - | 搜索关键词（标题、内容） |

#### 请求示例

```http
GET /api/v1/content/contents?page=1&size=10&content_type=announcement&is_published=true&search=公告 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取内容列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "content_type": "announcement",
        "title": "系统维护公告",
        "content": "系统将于今晚进行维护...",
        "is_published": true,
        "publish_at": "2024-01-01T10:00:00",
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:30:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

**失败响应 (403)**:
```json
{
  "code": 403,
  "message": "权限不足",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.2 创建内容

**接口**: `POST /api/v1/content/contents`  
**描述**: 创建新内容  
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| content_type | string | 是 | 内容类型，最大50字符 |
| title | string | 是 | 标题，最大200字符 |
| content | string | 否 | 内容正文 |
| is_published | boolean | 否 | 发布状态，默认false |
| publish_at | datetime | 否 | 发布时间 |

#### 请求示例

```http
POST /api/v1/content/contents HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "content_type": "announcement",
  "title": "新功能上线公告",
  "content": "我们很高兴地宣布新功能已经上线...",
  "is_published": true,
  "publish_at": "2024-01-01T10:00:00"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "创建内容成功",
  "data": {
    "id": 2,
    "content_type": "announcement",
    "title": "新功能上线公告",
    "content": "我们很高兴地宣布新功能已经上线...",
    "is_published": true,
    "publish_at": "2024-01-01T10:00:00",
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.3 获取内容详情

**接口**: `GET /api/v1/content/contents/{id}`  
**描述**: 获取指定内容的详细信息  
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 内容ID |

#### 请求示例

```http
GET /api/v1/content/contents/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取内容详情成功",
  "data": {
    "id": 1,
    "content_type": "announcement",
    "title": "系统维护公告",
    "content": "系统将于今晚进行维护...",
    "is_published": true,
    "publish_at": "2024-01-01T10:00:00",
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:30:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

**失败响应 (404)**:
```json
{
  "code": 404,
  "message": "内容不存在",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.4 更新内容

**接口**: `PUT /api/v1/content/contents/{id}`  
**描述**: 更新指定内容  
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 内容ID |

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| content_type | string | 否 | 内容类型，最大50字符 |
| title | string | 否 | 标题，最大200字符 |
| content | string | 否 | 内容正文 |
| is_published | boolean | 否 | 发布状态 |
| publish_at | datetime | 否 | 发布时间 |

#### 请求示例

```http
PUT /api/v1/content/contents/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "title": "系统维护公告（更新）",
  "content": "系统维护已完成，感谢您的耐心等待...",
  "is_published": true
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新内容成功",
  "data": {
    "id": 1,
    "content_type": "announcement",
    "title": "系统维护公告（更新）",
    "content": "系统维护已完成，感谢您的耐心等待...",
    "is_published": true,
    "publish_at": "2024-01-01T10:00:00",
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.5 删除内容

**接口**: `DELETE /api/v1/content/contents/{id}`  
**描述**: 删除指定内容  
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 内容ID |

#### 请求示例

```http
DELETE /api/v1/content/contents/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "删除内容成功",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.6 获取公告列表（公开接口）

**接口**: `GET /api/v1/content/public/announcements`  
**描述**: 获取已发布的公告列表  
**认证**: 不需要

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| limit | integer | 否 | 10 | 获取数量限制，最大50 |

#### 请求示例

```http
GET /api/v1/content/public/announcements?limit=5 HTTP/1.1
Host: localhost:8000
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取公告列表成功",
  "data": [
    {
      "id": 1,
      "content_type": "announcement",
      "title": "系统维护公告",
      "content": "系统将于今晚进行维护...",
      "is_published": true,
      "publish_at": "2024-01-01T10:00:00",
      "created_at": "2024-01-01T09:00:00",
      "updated_at": "2024-01-01T09:30:00"
    }
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.7 获取关于我们（公开接口）

**接口**: `GET /api/v1/content/public/about`  
**描述**: 获取关于我们页面内容  
**认证**: 不需要

#### 请求示例

```http
GET /api/v1/content/public/about HTTP/1.1
Host: localhost:8000
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取关于我们成功",
  "data": {
    "id": 5,
    "content_type": "about",
    "title": "关于燕友圈",
    "content": "燕友圈是一个专业的竞速榜单平台...",
    "is_published": true,
    "publish_at": "2024-01-01T10:00:00",
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:30:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

## 2. 播报列表模块

### 2.1 获取播报消息列表

**接口**: `GET /api/v1/content/broadcast-messages`  
**描述**: 获取播报消息列表  
**认证**: 不需要（公开接口）

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| limit | integer | 否 | 10 | 获取数量限制，最大50 |
| is_active | boolean | 否 | - | 激活状态筛选 |

#### 请求示例

```http
GET /api/v1/content/broadcast-messages?limit=5&is_active=true HTTP/1.1
Host: localhost:8000
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取播报消息列表成功",
  "data": [
    {
      "title": "欢迎参加本期竞速榜单",
      "content": "第10期5人竞速榜单现已开启报名...",
      "message_type": "info",
      "priority": 1,
      "is_active": true,
      "start_time": "2024-01-01T00:00:00",
      "end_time": "2024-01-31T23:59:59",
      "display_duration": 5,
      "target_audience": "all"
    }
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

### 2.2 创建播报消息

**接口**: `POST /api/v1/content/broadcast-messages`  
**描述**: 创建新的播报消息  
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| title | string | 是 | 标题，最大200字符 |
| content | string | 是 | 内容 |
| message_type | string | 是 | 消息类型，最大50字符 |
| priority | integer | 否 | 优先级，默认0 |
| is_active | boolean | 否 | 是否启用，默认true |
| start_time | datetime | 否 | 开始时间 |
| end_time | datetime | 否 | 结束时间 |
| display_duration | integer | 否 | 显示时长（秒），默认5 |
| target_audience | string | 否 | 目标受众，最大100字符 |

#### 请求示例

```http
POST /api/v1/content/broadcast-messages HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "title": "新榜单开启通知",
  "content": "第11期10人竞速榜单现已开启，欢迎大家踊跃参与！",
  "message_type": "announcement",
  "priority": 2,
  "is_active": true,
  "start_time": "2024-02-01T00:00:00",
  "end_time": "2024-02-28T23:59:59",
  "display_duration": 8,
  "target_audience": "all"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "创建播报消息成功",
  "data": {
    "title": "新榜单开启通知",
    "content": "第11期10人竞速榜单现已开启，欢迎大家踊跃参与！",
    "message_type": "announcement",
    "priority": 2,
    "is_active": true,
    "start_time": "2024-02-01T00:00:00",
    "end_time": "2024-02-28T23:59:59",
    "display_duration": 8,
    "target_audience": "all"
  },
  "timestamp": "2024-01-01T12:00:00"
}

## 3. 用户列表管理模块

### 3.1 获取用户列表

**接口**: `GET /api/v1/users`
**描述**: 获取用户列表，支持分页和筛选
**认证**: Bearer Token (管理员权限)

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| role | string | 否 | - | 用户角色筛选（user/admin/super_admin） |
| is_active | boolean | 否 | - | 激活状态筛选 |
| search | string | 否 | - | 搜索关键词（用户名、昵称） |

#### 请求示例

```http
GET /api/v1/users?page=1&size=10&role=user&is_active=true&search=张三 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "username": "zhangsan",
        "nickname": "张三",
        "avatar_url": "https://example.com/avatar1.jpg",
        "phone": "13800138001",
        "bio": "热爱竞速的玩家",
        "level": "江湖新人",
        "location": "北京市",
        "user_number": "YY00001",
        "gender": "男",
        "age": 25,
        "role": "user",
        "is_active": true,
        "is_verified": true,
        "points": 1000,
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:30:00",
        "last_login_at": "2024-01-01T11:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.2 获取用户详情

**接口**: `GET /api/v1/users/{user_id}`
**描述**: 获取指定用户的详细信息
**认证**: Bearer Token (管理员权限或本人)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

#### 请求示例

```http
GET /api/v1/users/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户详情成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "nickname": "张三",
    "avatar_url": "https://example.com/avatar1.jpg",
    "phone": "13800138001",
    "bio": "热爱竞速的玩家",
    "level": "江湖新人",
    "location": "北京市",
    "user_number": "YY00001",
    "gender": "男",
    "age": 25,
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "points": 1000,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:30:00",
    "last_login_at": "2024-01-01T11:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

**失败响应 (403)**:
```json
{
  "code": 403,
  "message": "权限不足",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.3 更新用户信息

**接口**: `PUT /api/v1/users`
**描述**: 更新用户信息
**认证**: Bearer Token (管理员权限或本人)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 用户ID |
| username | string | 否 | 用户名，3-50字符 |
| nickname | string | 否 | 昵称，最大100字符 |
| avatar_url | string | 否 | 头像URL，最大500字符 |
| phone | string | 否 | 手机号，最大20字符 |
| bio | string | 否 | 个人简介 |
| level | string | 否 | 用户等级，最大50字符 |
| location | string | 否 | 所在地，最大200字符 |
| user_number | string | 否 | 用户编号，3-50字符 |
| gender | string | 否 | 性别（男/女/不愿意透露） |
| age | integer | 否 | 年龄，0-150 |
| role | string | 否 | 用户角色（仅管理员可修改） |
| is_active | boolean | 否 | 激活状态（仅管理员可修改） |

#### 请求示例

```http
PUT /api/v1/users HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "id": 1,
  "nickname": "张三（更新）",
  "bio": "资深竞速玩家，热爱挑战",
  "level": "江湖高手",
  "location": "上海市",
  "age": 26
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "nickname": "张三（更新）",
    "avatar_url": "https://example.com/avatar1.jpg",
    "phone": "13800138001",
    "bio": "资深竞速玩家，热爱挑战",
    "level": "江湖高手",
    "location": "上海市",
    "user_number": "YY00001",
    "gender": "男",
    "age": 26,
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "points": 1000,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:00:00",
    "last_login_at": "2024-01-01T11:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```
```
